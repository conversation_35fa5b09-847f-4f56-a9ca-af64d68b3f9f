import { useGetUnreadCountQuery } from '@/store/api/chatApiSlice';

export const useUnreadMessages = () => {
  const userId = localStorage.getItem('userId');
  const shouldSkip = !userId;

  const { data, isLoading, error, refetch } = useGetUnreadCountQuery(
    undefined,
    {
      pollingInterval: 0,
      skip: shouldSkip,
    }
  );

  // Create a safe refetch function that only calls refetch if the query is not skipped
  const safeRefetch = () => {
    if (!shouldSkip) {
      refetch();
    }
  };

  return {
    unreadCount: data?.unreadCount || 0,
    isLoading,
    error,
    refetch: safeRefetch,
  };
};
